package space.lzhq.ph.controller

import cn.binarywang.wx.miniapp.api.WxMaService
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult
import cn.hutool.core.exceptions.ExceptionUtil
import cn.hutool.core.io.FileUtil
import cn.hutool.core.util.IdUtil
import cn.hutool.extra.qrcode.QrCodeUtil
import cn.hutool.extra.qrcode.QrConfig
import com.github.binarywang.wxpay.bean.notify.WxInsuranceOrderNotifyResponse
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse
import com.github.binarywang.wxpay.bean.result.WxPayRefundQueryResult
import com.github.binarywang.wxpay.service.WxPayService
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.exception.file.FileNameLengthLimitExceededException
import com.ruoyi.common.exception.file.FileSizeLimitExceededException
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken
import me.chanjar.weixin.common.bean.ocr.WxOcrIdCardResult
import me.chanjar.weixin.common.error.WxErrorException
import me.chanjar.weixin.mp.api.WxMpService
import org.mospital.dongruan.pay.PaymentNotification
import org.mospital.dongruan.pay.PaymentNotificationResponse
import org.mospital.jackson.JacksonKit
import org.mospital.wecity.mip.WeixinMipSetting
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.common.HisQrCode
import space.lzhq.ph.domain.Session
import space.lzhq.ph.ext.FileUploader
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.ISessionService
import space.lzhq.ph.service.IWxRefundService
import java.awt.Color

@RestController
@RequestMapping("/open/wx")
class WxController : BaseController() {

    @Autowired
    private lateinit var wxMaService: WxMaService

    @Autowired
    private lateinit var wxMpService: WxMpService

    @Autowired
    private lateinit var sessionService: ISessionService

    @Autowired
    private lateinit var wxRefundService: IWxRefundService

    @Autowired
    private lateinit var wxPayService: WxPayService

    @PostMapping("login")
    fun login(@RequestParam("code") code: String): AjaxResult {
        val code2sessionResult: WxMaJscode2SessionResult = try {
            wxMaService.userService.getSessionInfo(code)
        } catch (e: WxErrorException) {
            return AjaxResult.error(e.error.errorMsg)
        }

        val sid: String = IdUtil.fastUUID()

        sessionService.deleteSessionByOpenId(code2sessionResult.openid)
        val newWxSession = Session().apply {
            this.id = sid
            this.clientType = ClientType.WEIXIN_MA.code
            this.openId = code2sessionResult.openid
            this.unionId = code2sessionResult.unionid ?: ""
            this.sessionKey = code2sessionResult.sessionKey
        }
        sessionService.insertSession(newWxSession)

        response.setHeader(HttpHeaders.AUTHORIZATION, sid)
        return AjaxResult.success(mapOf(HttpHeaders.AUTHORIZATION to sid))
    }

    @PostMapping("mpLogin")
    fun mpLogin(@RequestParam("code") code: String): AjaxResult {
        val wxOAuth2AccessToken: WxOAuth2AccessToken = try {
            wxMpService.oAuth2Service.getAccessToken(code)
        } catch (e: WxErrorException) {
            return AjaxResult.error(e.error.errorMsg)
        }

        val sid: String = IdUtil.fastUUID()

        sessionService.deleteSessionByOpenId(wxOAuth2AccessToken.openId)
        val newWxSession = Session().apply {
            this.id = sid
            this.clientType = ClientType.WEIXIN_MP.code
            this.openId = wxOAuth2AccessToken.openId
            this.unionId = wxOAuth2AccessToken.unionId ?: ""
            this.sessionKey = ""
        }
        sessionService.insertSession(newWxSession)

        response.setHeader(HttpHeaders.AUTHORIZATION, sid)
        return AjaxResult.success(mapOf(HttpHeaders.AUTHORIZATION to sid))
    }

    @PostMapping("ma/idCardOcr")
    @RequireSession(ClientType.WEIXIN_MA)
    fun maIdCardOcr(file: MultipartFile): AjaxResult {
        return try {
            AjaxResult.success(doIdCardOcr(file))
        } catch (e: Exception) {
            logger.error("idCardOcr failed", e)
            AjaxResult.error(e.message)
        }
    }

    @PostMapping("mp/idCardOcr")
    @RequireSession(ClientType.WEIXIN_MP)
    fun mpIdCardOcr(file: MultipartFile): AjaxResult {
        return try {
            AjaxResult.success(doIdCardOcr(file))
        } catch (e: Exception) {
            logger.error("idCardOcr failed", e)
            AjaxResult.error(e.message)
        }
    }

    // 小程序和公众号共用的身份证识别接口，目前都是调用小程序的识别接口
    private fun doIdCardOcr(file: MultipartFile): WxOcrIdCardResult {
        val fileNameLength = file.originalFilename!!.length
        if (fileNameLength > FileUploader.DEFAULT_FILE_NAME_LENGTH) {
            throw FileNameLengthLimitExceededException(FileUploader.DEFAULT_FILE_NAME_LENGTH)
        }

        val size = file.size
        if (size > 2 * 1024 * 1024) {
            throw FileSizeLimitExceededException(2)
        }

        val extensionName = FileUploader.getExtension(file)
        val tmpFile = FileUtil.createTempFile("ocr-", ".$extensionName", true)
        file.transferTo(tmpFile)
        val result = WeixinExt.idCardOcr(tmpFile)
        FileUtil.del(tmpFile)
        return result
    }

    private fun ok(msg: String = "SUCCESS"): String = WxPayNotifyResponse.success(msg)
    private fun fail(msg: String = "FAIL"): String = WxPayNotifyResponse.fail(msg)

    @PostMapping("onWXPay")
    fun onWXPay(@RequestBody xmlData: String): String {
        return ok()
    }

    @PostMapping("onDongruanPay")
    fun onDongruanPay(@RequestBody rawData: String): String {
        logger.debug("onDongruanPay: $rawData")

        // 解析支付通知数据
        val notification = try {
            JacksonKit.readValue(rawData, PaymentNotification::class.java)
        } catch (e: Exception) {
            logger.error("解析支付通知数据失败", e)
            return PaymentNotificationResponse.fail("解析支付通知数据失败").toStringResponse()
        }

        if (notification.isRefundNotification())
        val zyPayNo = notification.bizContent.orderNo
        return "success"
    }

    @PostMapping("onWXRefund")
    fun onWXRefund(@RequestBody xmlData: String): String {
        try {
            val wxPayRefundNotifyResult = wxPayService.parseRefundNotifyResult(xmlData)
            val zyRefundNo = wxPayRefundNotifyResult.reqInfo.outRefundNo
            val wxRefund = wxRefundService.selectWxRefundByZyRefundNo(zyRefundNo)
                ?: return fail("本地无此订单: 掌医退款单号=$zyRefundNo")
            if (!wxRefund.wxRefundNo.isNullOrBlank()) {
                // 本地订单状态已更新
                return ok()
            }

            val wxPayRefundQueryResult = wxPayService.refundQuery(null, null, zyRefundNo, null)
            val refundRecord: WxPayRefundQueryResult.RefundRecord = wxPayRefundQueryResult.refundRecords[0]
            val ok = wxRefundService.updateOnWXRefund(wxRefund, refundRecord)
            return if (ok) {
                ok()
            } else {
                fail("更新退款记录失败")
            }
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return fail(ExceptionUtil.stacktraceToOneLineString(e, 100))
        }
    }

    @PostMapping("onWxInsurancePay")
    fun onWxInsurancePay(@RequestBody xmlData: String): String {
        logger.debug("notify: onWxInsurancePay: $xmlData")
        val response = WxInsuranceOrderNotifyResponse.success(signKey = WeixinMipSetting.ma.mipKey)
        return response.toXML()
    }

    @GetMapping("insuranceOrder/{payOrderId}")
    fun insuranceOrder(@PathVariable("payOrderId") payOrderId: String): String {
        logger.debug("return: onWxInsurancePay: $payOrderId")
        return payOrderId
    }

    private fun buildAuthorizationUrl(
        @RequestParam("redirectUri") redirectUri: String,
        @RequestParam("scope") scope: String,
        @RequestParam("state") state: String,
    ): String {
        return wxMpService.oAuth2Service.buildAuthorizationUrl(redirectUri, scope, state)
    }

    /**
     * 生成二维码
     *
     * @param patientCard 患者卡号
     * @param scene 场景，当前支持的场景有：
     *              - QRCODE_ZJJF：诊间缴费
     */
    @GetMapping("qrCode")
    fun generateQrCode(
        @RequestParam("patientCard") patientCard: String,
        @RequestParam("scene") scene: String
    ) {
        try {
            val supportedScenes = arrayOf("QRCODE_ZJJF")
            require(scene in supportedScenes) {
                "不支持的场景: $scene"
            }

            require(patientCard.length == 64) {
                "患者卡号不正确"
            }

            val expiredTimestamp = System.currentTimeMillis() + 43200000 // 12 小时
            val hisQrCode = HisQrCode(
                patientCard = patientCard,
                expiredTimestamp = expiredTimestamp
            )
            val token = hisQrCode.getToken()
            val authorizationUrl = buildAuthorizationUrl(
                redirectUri = "https://xjzybyy.xjyqtl.cn/mp/index.html?token=$token",
                scope = "snsapi_base",
                state = scene
            )
            
            val qrConfig = QrConfig.create()
                .setWidth(800)
                .setHeight(800)
                .setForeColor(Color.BLACK)
                .setBackColor(Color.WHITE)
                .setRatio(4)
                .setErrorCorrection(ErrorCorrectionLevel.H)
                .setMargin(2)
                
            response.contentType = "image/png"
            response.setHeader("Cache-Control", "no-store")
            response.outputStream.use { outputStream ->
                QrCodeUtil.generate(authorizationUrl, qrConfig, "png", outputStream)
            }
        } catch (e: Exception) {
            logger.error("生成二维码失败", e)
            throw e
        }
    }
}
